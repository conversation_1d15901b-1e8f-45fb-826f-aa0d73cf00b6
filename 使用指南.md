# 分布式数据库系统使用指南

## 🎯 系统简介

本系统是一个基于2PC（二阶段提交）协议的分布式数据库事务管理系统，包含以下核心功能：

- ✅ **2PC分布式事务管理**：完整实现二阶段提交协议
- ✅ **分布式数据库应用**：银行转账、库存管理等业务场景
- ✅ **Web可视化界面**：实时监控和管理系统
- ✅ **故障恢复机制**：网络故障和节点故障处理

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

#### Windows用户
双击运行 `start.bat` 文件，然后按照菜单提示操作：

```
1. 🎬 观看系统演示        # 先看演示了解系统
2. 🚀 完整系统设置        # 一键设置整个系统
3. 🌐 启动Web界面        # 启动Web管理界面
```

#### Linux/Mac用户
在终端中运行：
```bash
./start.sh
```

### 方法二：手动安装

#### 1. 环境准备
```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Docker（用于MySQL数据库）
# Windows: 下载Docker Desktop
# Linux: sudo apt-get install docker.io
# Mac: brew install docker
```

#### 2. 启动数据库
```bash
# 启动MySQL容器
python main.py start-db

# 或者手动启动
docker run -d --name mysql1 -e MYSQL_ROOT_PASSWORD=password -p 3306:3306 mysql:8.4
docker run -d --name mysql2 -e MYSQL_ROOT_PASSWORD=password -p 3307:3306 mysql:8.4
```

#### 3. 初始化数据库
```bash
python main.py init-db
```

#### 4. 启动Web界面
```bash
python main.py web
```

然后在浏览器中访问：http://localhost:5000

## 📖 功能使用说明

### 1. 系统演示

运行演示程序了解系统工作原理：
```bash
python demo.py
```

演示内容包括：
- 2PC协议工作流程
- 银行转账场景
- 库存管理场景
- 故障处理机制

### 2. Web管理界面

访问 http://localhost:5000 使用Web界面：

#### 仪表板页面
- **账户管理**：创建新账户
- **资金转账**：执行跨数据库转账
- **库存管理**：查看和管理产品库存
- **订单处理**：处理客户订单

#### 事务管理页面
- **2PC协议演示**：可视化展示2PC工作流程
- **事务测试工具**：测试不同场景下的事务处理
- **事务日志**：查看详细的事务执行记录

#### 系统监控页面
- **实时监控**：系统性能和数据库状态
- **系统日志**：实时查看系统运行日志
- **配置管理**：调整系统参数

### 3. 命令行工具

```bash
# 查看所有可用命令
python main.py --help

# 常用命令
python main.py setup      # 完整系统设置
python main.py start-db   # 启动数据库
python main.py init-db    # 初始化数据库
python main.py test       # 运行测试
python main.py web        # 启动Web界面
python main.py demo       # 运行演示
python main.py status     # 查看系统状态
```

## 🧪 测试系统

### 运行测试套件
```bash
python main.py test
```

### 手动测试场景

#### 1. 银行转账测试
```python
from distributed_app import BankingService

banking = BankingService()

# 创建账户
banking.create_account(1001, 5000.0)
banking.create_account(1002, 3000.0)

# 执行转账
success = banking.transfer_money(1001, 1002, 500.0)
print(f"转账结果: {'成功' if success else '失败'}")

# 查看余额
balance1 = banking.get_account_balance(1001)
balance2 = banking.get_account_balance(1002)
print(f"账户1001余额: {balance1}")
print(f"账户1002余额: {balance2}")
```

#### 2. 库存管理测试
```python
from distributed_app import InventoryService

inventory = InventoryService()

# 处理订单
success = inventory.process_order(101, 2, 2001)
print(f"订单处理结果: {'成功' if success else '失败'}")
```

## 🔧 配置说明

### 环境配置文件

复制 `.env.example` 为 `.env` 并根据需要修改：

```bash
# 数据库配置
DB1_HOST=localhost
DB1_PORT=3306
DB1_USER=root
DB1_PASSWORD=password

DB2_HOST=localhost
DB2_PORT=3307
DB2_USER=root
DB2_PASSWORD=password

# 事务配置
TRANSACTION_TIMEOUT=60
MAX_RETRY_ATTEMPTS=3

# Web界面配置
WEB_HOST=0.0.0.0
WEB_PORT=5000
DEBUG=False
```

### 系统参数调优

在Web界面的系统监控页面可以调整：
- 事务超时时间
- 最大重试次数
- 连接池大小
- 调试模式开关

## 🐛 故障排除

### 常见问题及解决方案

#### 1. 数据库连接失败
**问题**：无法连接到MySQL数据库
**解决方案**：
```bash
# 检查Docker容器状态
docker ps

# 重启数据库容器
python main.py stop-db
python main.py start-db

# 检查端口是否被占用
netstat -an | grep 3306
netstat -an | grep 3307
```

#### 2. Web界面无法访问
**问题**：浏览器无法打开 http://localhost:5000
**解决方案**：
```bash
# 检查端口是否被占用
netstat -an | grep 5000

# 修改端口配置
# 编辑 .env 文件，修改 WEB_PORT=5001

# 检查防火墙设置
# Windows: 允许Python通过防火墙
# Linux: sudo ufw allow 5000
```

#### 3. 依赖安装失败
**问题**：pip install 失败
**解决方案**：
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 单独安装问题包
pip install mysql-connector-python
pip install flask
```

#### 4. 事务执行失败
**问题**：分布式事务回滚
**解决方案**：
- 检查数据库连接状态
- 查看系统日志了解具体错误
- 确认账户余额或库存是否充足
- 检查网络连接稳定性

### 日志查看

```bash
# 查看系统日志文件
tail -f logs/distributed_db.log

# 在Web界面查看实时日志
# 访问 http://localhost:5000/monitoring

# 查看Docker容器日志
docker logs mysql1
docker logs mysql2
```

## 📊 性能监控

### 系统指标

在Web界面的监控页面可以查看：
- **事务吞吐量**：每秒处理的事务数
- **响应时间**：事务平均响应时间
- **成功率**：事务成功提交的比例
- **系统负载**：CPU和内存使用情况

### 性能优化建议

1. **调整连接池大小**：根据并发需求调整
2. **优化事务超时时间**：平衡性能和可靠性
3. **监控数据库性能**：确保MySQL配置合理
4. **网络优化**：确保数据库间网络稳定

## 🎓 学习建议

### 理解2PC协议

1. **阅读代码**：从 `transaction_manager.py` 开始
2. **观看演示**：运行 `python demo.py` 了解流程
3. **实际操作**：在Web界面进行事务操作
4. **查看日志**：观察事务执行过程

### 扩展实验

1. **模拟故障**：手动停止数据库容器观察系统反应
2. **并发测试**：同时执行多个事务
3. **性能测试**：测试系统在高负载下的表现
4. **代码修改**：尝试添加新的业务场景

## 📞 技术支持

如果遇到问题，可以：

1. **查看文档**：README.md 和 项目总结.md
2. **检查日志**：系统日志通常包含详细错误信息
3. **运行测试**：`python main.py test` 检查系统状态
4. **重新初始化**：`python main.py setup` 重新设置系统

## 🎉 开始使用

现在您可以开始使用分布式数据库系统了！

**推荐的学习路径**：
1. 运行 `python demo.py` 观看系统演示
2. 执行 `python main.py setup` 设置系统
3. 启动 `python main.py web` 使用Web界面
4. 在Web界面中尝试各种功能
5. 查看监控页面了解系统运行状态

祝您使用愉快！🚀
