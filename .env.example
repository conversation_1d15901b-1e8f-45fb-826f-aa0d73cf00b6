# 分布式数据库系统环境配置文件
# 复制此文件为 .env 并根据实际环境修改配置

# 数据库1配置（Docker容器）
DB1_HOST=localhost
DB1_PORT=3316
DB1_USER=root
DB1_PASSWORD=password
DB1_DATABASE=db1

# 数据库2配置（Docker容器）
DB2_HOST=localhost
DB2_PORT=3317
DB2_USER=root
DB2_PASSWORD=password
DB2_DATABASE=db2

# 连接池配置
CONNECTION_POOL_SIZE=5
CONNECTION_TIMEOUT=30

# 事务配置
TRANSACTION_TIMEOUT=60
MAX_RETRY_ATTEMPTS=3
RETRY_INTERVAL=1
PREPARE_TIMEOUT=30

# Web界面配置
SECRET_KEY=your-secret-key-here-change-in-production
WEB_HOST=0.0.0.0
WEB_PORT=5000
DEBUG=False
SOCKETIO_ASYNC_MODE=eventlet

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/distributed_db.log
MAX_LOG_SIZE=10
BACKUP_COUNT=5
