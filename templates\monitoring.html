{% extends "base.html" %}

{% block title %}系统监控 - 分布式数据库系统{% endblock %}

{% block page_title %}系统监控{% endblock %}

{% block content %}
<!-- 系统概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body text-center">
                <h3 id="system-uptime">00:00:00</h3>
                <p>系统运行时间</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body text-center">
                <h3 id="total-requests">0</h3>
                <p>总请求数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body text-center">
                <h3 id="avg-response-time">0ms</h3>
                <p>平均响应时间</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body text-center">
                <h3 id="error-rate">0%</h3>
                <p>错误率</p>
            </div>
        </div>
    </div>
</div>

<!-- 数据库节点状态 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database"></i>
                    数据库节点状态
                </h5>
            </div>
            <div class="card-body">
                <div id="database-nodes">
                    <!-- 动态加载数据库节点信息 -->
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-network-wired"></i>
                    网络连接状态
                </h5>
            </div>
            <div class="card-body">
                <canvas id="network-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 性能监控图表 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    事务处理性能
                </h5>
            </div>
            <div class="card-body">
                <canvas id="performance-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt"></i>
                    系统负载
                </h5>
            </div>
            <div class="card-body">
                <canvas id="load-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 实时日志监控 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-terminal"></i>
                    实时系统日志
                </h5>
                <div>
                    <select class="form-select form-select-sm d-inline-block w-auto me-2" id="log-level-filter">
                        <option value="all">所有级别</option>
                        <option value="error">错误</option>
                        <option value="warning">警告</option>
                        <option value="info">信息</option>
                        <option value="debug">调试</option>
                    </select>
                    <button class="btn btn-sm btn-outline-light" onclick="clearSystemLogs()">
                        <i class="fas fa-trash"></i> 清空
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="system-logs" class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto; font-family: monospace;">
                    <div class="text-muted">等待日志数据...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统配置和控制 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i>
                    系统配置
                </h5>
            </div>
            <div class="card-body">
                <form id="system-config-form">
                    <div class="mb-3">
                        <label class="form-label">事务超时时间 (秒)</label>
                        <input type="number" class="form-control" id="transaction-timeout" value="60">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">最大重试次数</label>
                        <input type="number" class="form-control" id="max-retries" value="3">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">连接池大小</label>
                        <input type="number" class="form-control" id="pool-size" value="5">
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="debug-mode">
                            <label class="form-check-label" for="debug-mode">
                                调试模式
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存配置
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools"></i>
                    系统控制
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="testConnections()">
                        <i class="fas fa-plug"></i> 测试数据库连接
                    </button>
                    <button class="btn btn-info" onclick="refreshStats()">
                        <i class="fas fa-sync-alt"></i> 刷新统计数据
                    </button>
                    <button class="btn btn-warning" onclick="clearCache()">
                        <i class="fas fa-broom"></i> 清空缓存
                    </button>
                    <button class="btn btn-secondary" onclick="exportSystemReport()">
                        <i class="fas fa-file-export"></i> 导出系统报告
                    </button>
                </div>
                
                <hr>
                
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>危险操作</strong>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-danger" onclick="restartSystem()">
                        <i class="fas fa-redo"></i> 重启系统
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let performanceChart, loadChart, networkChart;
    let systemStartTime = Date.now();
    let logBuffer = [];
    
    // 初始化图表
    function initCharts() {
        // 性能图表
        const perfCtx = document.getElementById('performance-chart').getContext('2d');
        performanceChart = new Chart(perfCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '事务/秒',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // 负载图表
        const loadCtx = document.getElementById('load-chart').getContext('2d');
        loadChart = new Chart(loadCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'CPU使用率',
                        data: [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)'
                    },
                    {
                        label: '内存使用率',
                        data: [],
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
        
        // 网络图表
        const netCtx = document.getElementById('network-chart').getContext('2d');
        networkChart = new Chart(netCtx, {
            type: 'doughnut',
            data: {
                labels: ['正常连接', '超时连接', '失败连接'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    // 更新系统运行时间
    function updateUptime() {
        const uptime = Date.now() - systemStartTime;
        const hours = Math.floor(uptime / (1000 * 60 * 60));
        const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((uptime % (1000 * 60)) / 1000);
        
        document.getElementById('system-uptime').textContent = 
            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    // 更新数据库节点状态
    function updateDatabaseNodes() {
        fetch('/api/system/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const container = document.getElementById('database-nodes');
                    container.innerHTML = '';
                    
                    for (const [nodeId, status] of Object.entries(data.data)) {
                        const nodeDiv = document.createElement('div');
                        nodeDiv.className = 'mb-3 p-3 border rounded';
                        nodeDiv.innerHTML = `
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">
                                        <span class="status-indicator ${status.available ? 'status-online' : 'status-offline'}"></span>
                                        ${nodeId.toUpperCase()}
                                    </h6>
                                    <small class="text-muted">${status.host}:${status.port}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge ${status.available ? 'bg-success' : 'bg-danger'}">
                                        ${status.available ? '在线' : '离线'}
                                    </span>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small>数据库: ${status.database}</small><br>
                                <small>最后检查: ${status.last_check ? new Date(status.last_check * 1000).toLocaleTimeString() : '未知'}</small>
                            </div>
                        `;
                        container.appendChild(nodeDiv);
                    }
                }
            })
            .catch(error => {
                console.error('Error updating database nodes:', error);
            });
    }
    
    // 更新性能数据
    function updatePerformanceData() {
        const now = new Date().toLocaleTimeString();
        const tps = Math.floor(Math.random() * 100) + 20; // 模拟TPS数据
        
        // 更新性能图表
        if (performanceChart.data.labels.length >= 20) {
            performanceChart.data.labels.shift();
            performanceChart.data.datasets[0].data.shift();
        }
        
        performanceChart.data.labels.push(now);
        performanceChart.data.datasets[0].data.push(tps);
        performanceChart.update('none');
        
        // 更新负载图表
        const cpu = Math.floor(Math.random() * 80) + 10;
        const memory = Math.floor(Math.random() * 70) + 20;
        
        if (loadChart.data.labels.length >= 20) {
            loadChart.data.labels.shift();
            loadChart.data.datasets[0].data.shift();
            loadChart.data.datasets[1].data.shift();
        }
        
        loadChart.data.labels.push(now);
        loadChart.data.datasets[0].data.push(cpu);
        loadChart.data.datasets[1].data.push(memory);
        loadChart.update('none');
        
        // 更新网络图表
        const normal = Math.floor(Math.random() * 50) + 40;
        const timeout = Math.floor(Math.random() * 10) + 2;
        const failed = Math.floor(Math.random() * 5) + 1;
        
        networkChart.data.datasets[0].data = [normal, timeout, failed];
        networkChart.update();
        
        // 更新统计数据
        document.getElementById('total-requests').textContent = Math.floor(Math.random() * 10000) + 5000;
        document.getElementById('avg-response-time').textContent = Math.floor(Math.random() * 100) + 50 + 'ms';
        document.getElementById('error-rate').textContent = (Math.random() * 5).toFixed(2) + '%';
    }
    
    // 添加系统日志
    function addSystemLog(level, message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        
        logBuffer.push({ level, entry: logEntry, timestamp });
        
        // 限制日志缓冲区大小
        if (logBuffer.length > 1000) {
            logBuffer.shift();
        }
        
        updateLogDisplay();
    }
    
    // 更新日志显示
    function updateLogDisplay() {
        const logContainer = document.getElementById('system-logs');
        const levelFilter = document.getElementById('log-level-filter').value;
        
        let filteredLogs = logBuffer;
        if (levelFilter !== 'all') {
            filteredLogs = logBuffer.filter(log => log.level === levelFilter);
        }
        
        const logHtml = filteredLogs.slice(-50).map(log => {
            const colorClass = {
                'error': 'text-danger',
                'warning': 'text-warning',
                'info': 'text-info',
                'debug': 'text-muted'
            }[log.level] || 'text-light';
            
            return `<div class="${colorClass}">${log.entry}</div>`;
        }).join('');
        
        logContainer.innerHTML = logHtml || '<div class="text-muted">暂无日志数据</div>';
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    // 清空系统日志
    function clearSystemLogs() {
        logBuffer = [];
        updateLogDisplay();
    }
    
    // 系统控制函数
    function testConnections() {
        addSystemLog('info', '开始测试数据库连接...');
        
        fetch('/api/system/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let allOnline = true;
                    for (const [nodeId, status] of Object.entries(data.data)) {
                        if (status.available) {
                            addSystemLog('info', `数据库 ${nodeId} 连接正常`);
                        } else {
                            addSystemLog('error', `数据库 ${nodeId} 连接失败`);
                            allOnline = false;
                        }
                    }
                    
                    if (allOnline) {
                        showMessage('所有数据库连接正常', 'success');
                    } else {
                        showMessage('部分数据库连接异常', 'warning');
                    }
                }
            })
            .catch(error => {
                addSystemLog('error', `连接测试失败: ${error.message}`);
                showMessage('连接测试失败', 'error');
            });
    }
    
    function refreshStats() {
        addSystemLog('info', '刷新统计数据...');
        updatePerformanceData();
        updateDatabaseNodes();
        showMessage('统计数据已刷新', 'success');
    }
    
    function clearCache() {
        addSystemLog('info', '清空系统缓存...');
        showMessage('缓存已清空', 'success');
    }
    
    function exportSystemReport() {
        addSystemLog('info', '导出系统报告...');
        showMessage('系统报告导出功能开发中...', 'info');
    }
    
    function restartSystem() {
        if (confirm('确定要重启系统吗？这将中断所有正在进行的事务。')) {
            addSystemLog('warning', '系统重启请求...');
            showMessage('系统重启功能需要管理员权限', 'warning');
        }
    }
    
    // 事件监听器
    document.getElementById('log-level-filter').addEventListener('change', updateLogDisplay);
    
    document.getElementById('system-config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        addSystemLog('info', '保存系统配置...');
        showMessage('配置已保存', 'success');
    });
    
    // Socket.IO事件监听
    socket.on('system_status', function(data) {
        // 实时更新数据库状态
        updateDatabaseNodes();
    });
    
    socket.on('transfer_completed', function(data) {
        addSystemLog('info', `转账事务完成: ${data.from_account} → ${data.to_account}, ¥${data.amount}`);
    });
    
    socket.on('order_processed', function(data) {
        addSystemLog('info', `订单处理完成: 产品 ${data.product_id}, 数量 ${data.quantity}`);
    });
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        initCharts();
        updateDatabaseNodes();
        
        // 定期更新数据
        setInterval(updateUptime, 1000);
        setInterval(updatePerformanceData, 5000);
        setInterval(updateDatabaseNodes, 10000);
        
        // 模拟一些初始日志
        addSystemLog('info', '系统监控模块已启动');
        addSystemLog('info', '开始监控数据库连接状态');
        addSystemLog('info', '性能监控已激活');
    });
</script>
{% endblock %}
