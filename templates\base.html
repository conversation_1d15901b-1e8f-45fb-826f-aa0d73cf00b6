<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}分布式数据库系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-online {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        .status-offline {
            background-color: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .transaction-log {
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            border-left: 3px solid #007bff;
            padding-left: 10px;
            margin-bottom: 10px;
        }
        .log-success {
            border-left-color: #28a745;
        }
        .log-error {
            border-left-color: #dc3545;
        }
        .log-warning {
            border-left-color: #ffc107;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-database"></i>
                            分布式DB系统
                        </h4>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('transactions') }}">
                                <i class="fas fa-exchange-alt"></i>
                                事务管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('monitoring') }}">
                                <i class="fas fa-chart-line"></i>
                                系统监控
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="text-white">
                    
                    <!-- 系统状态 -->
                    <div class="text-white">
                        <h6>数据库状态</h6>
                        <div id="db-status">
                            <div class="mb-2">
                                <span class="status-indicator status-offline" id="db1-status"></span>
                                <small>数据库1</small>
                            </div>
                            <div class="mb-2">
                                <span class="status-indicator status-offline" id="db2-status"></span>
                                <small>数据库2</small>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}仪表板{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-btn">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 消息提示区域 -->
                <div id="message-area"></div>
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Socket.IO连接
        const socket = io();
        
        // 连接事件
        socket.on('connect', function() {
            console.log('Connected to server');
            showMessage('已连接到服务器', 'success');
        });
        
        socket.on('disconnect', function() {
            console.log('Disconnected from server');
            showMessage('与服务器连接断开', 'warning');
        });
        
        // 系统状态更新
        socket.on('system_status', function(data) {
            updateDatabaseStatus(data.databases);
        });
        
        // 更新数据库状态指示器
        function updateDatabaseStatus(databases) {
            for (const [dbId, status] of Object.entries(databases)) {
                const indicator = document.getElementById(dbId + '-status');
                if (indicator) {
                    if (status.available) {
                        indicator.className = 'status-indicator status-online';
                    } else {
                        indicator.className = 'status-indicator status-offline';
                    }
                }
            }
        }
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('message-area');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            messageArea.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
        
        // 刷新按钮
        document.getElementById('refresh-btn').addEventListener('click', function() {
            location.reload();
        });
        
        // 请求初始状态
        socket.emit('request_status');
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
